# frozen_string_literal: true

module SentryBench
  class ReportGenerator
    attr_reader :main_report, :baseline_report, :transport_stats, :iterations, :git_info

    def initialize(main_report:, baseline_report:, transport_stats:, iterations:, git_info:)
      @main_report = main_report
      @baseline_report = baseline_report
      @transport_stats = transport_stats
      @iterations = iterations
      @git_info = git_info
    end

    def generate
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
            <title>Sentry Benchmark Report (sentry-bench)</title>
            <meta charset="utf-8">
            #{css_styles}
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        </head>
        <body>
            #{header_section}
            #{executive_summary_section}
            #{transport_statistics_section}
            #{memory_breakdown_section}
            #{allocation_analysis_section}
            #{retention_analysis_section}
            #{file_analysis_section}
            #{recommendations_section}
        </body>
        </html>
      HTML
    end

    private

    def css_styles
      <<~CSS
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9f4ff; border-radius: 3px; min-width: 200px; }
            .memory-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .memory-table th, .memory-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .memory-table th { background-color: #f2f2f2; font-weight: bold; }
            .memory-table tr:nth-child(even) { background-color: #f9f9f9; }
            .chart-container { margin: 20px 0; padding: 15px; background: #fafafa; border-radius: 5px; }
            pre { background: #f8f8f8; padding: 15px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 3px; margin: 10px 0; }
            .code-location { font-family: monospace; font-size: 11px; color: #666; }
            .progress-bar { width: 100%; background-color: #e0e0e0; border-radius: 3px; overflow: hidden; }
            .progress-fill { height: 20px; background-color: #4CAF50; text-align: center; line-height: 20px; color: white; font-size: 12px; }
            .gem-badge { background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; }
        </style>
      CSS
    end

    def header_section
      <<~HTML
        <div class="header">
            <h1>🔍 Sentry Benchmark Report <span class="gem-badge">sentry-bench v#{SentryBench::VERSION}</span></h1>
            <p><strong>Generated:</strong> #{Time.now.strftime('%Y-%m-%d %H:%M:%S %Z')}</p>
            <p><strong>Iterations:</strong> #{format_number(iterations)}</p>
            <p><strong>Ruby Version:</strong> #{RUBY_VERSION}</p>
            <p><strong>Sentry Version:</strong> #{defined?(Sentry::VERSION) ? Sentry::VERSION : "unknown"}</p>
            <p><strong>Git SHA:</strong> #{git_info[:short_sha]} (#{git_info[:branch]})</p>
            <p><strong>Commit:</strong> #{git_info[:commit_message].split("\n").first}</p>
        </div>
      HTML
    end

    def executive_summary_section
      retention_rate = (main_report.total_retained.to_f / main_report.total_allocated * 100).round(2)
      avg_memory_per_iteration = main_report.total_allocated_memsize / iterations

      <<~HTML
        <div class="section">
            <h2>📊 Executive Summary</h2>
            <div class="metric">
                <strong>Total Allocated Objects:</strong><br>
                #{format_number(main_report.total_allocated)}
            </div>
            <div class="metric">
                <strong>Total Retained Objects:</strong><br>
                #{format_number(main_report.total_retained)}
            </div>
            <div class="metric">
                <strong>Memory Allocated:</strong><br>
                #{format_bytes(main_report.total_allocated_memsize)}
            </div>
            <div class="metric">
                <strong>Memory Retained:</strong><br>
                #{format_bytes(main_report.total_retained_memsize)}
            </div>
            <div class="metric">
                <strong>Retention Rate:</strong><br>
                #{retention_rate}%
            </div>
            <div class="metric">
                <strong>Avg Memory/Iteration:</strong><br>
                #{format_bytes(avg_memory_per_iteration)}
            </div>
        </div>
      HTML
    end

    def transport_statistics_section
      <<~HTML
        <div class="section">
            <h2>🚀 Transport Statistics</h2>
            <table class="memory-table">
                <tr><th>Metric</th><th>Count</th><th>Memory Usage</th></tr>
                <tr><td>Events Captured</td><td>#{transport_stats[:events]}</td><td>#{format_bytes(transport_stats[:events_memory])}</td></tr>
                <tr><td>Envelopes Created</td><td>#{transport_stats[:envelopes]}</td><td>#{format_bytes(transport_stats[:envelopes_memory])}</td></tr>
                <tr><td>Log Events</td><td>#{transport_stats[:log_events]}</td><td>#{format_bytes(transport_stats[:log_events_memory])}</td></tr>
            </table>
        </div>
      HTML
    end

    def memory_breakdown_section
      classes_to_analyze = [
        "String", "Hash", "Array", "Time", "Sentry::LogEvent",
        "Sentry::Envelope", "Sentry::Event", "Logger::LogDevice"
      ]

      # Convert arrays to hashes for easier lookup
      allocated_by_class = main_report.allocated_objects_by_class.to_h { |item| [item[:data], item[:count]] }
      retained_by_class = main_report.retained_objects_by_class.to_h { |item| [item[:data], item[:count]] }

      breakdown_html = classes_to_analyze.map do |klass|
        allocated_count = allocated_by_class[klass] || 0
        retained_count = retained_by_class[klass] || 0

        "<tr>" \
          "<td>#{klass}</td>" \
          "<td>#{allocated_count}</td>" \
          "<td>#{retained_count}</td>" \
        "</tr>"
      end.join

      <<~HTML
        <div class="section">
            <h2>🧠 Memory Breakdown by Object Type</h2>
            <table class="memory-table">
                <tr><th>Object Type</th><th>Allocated</th><th>Retained</th></tr>
                #{breakdown_html}
            </table>
        </div>
      HTML
    end

    def allocation_analysis_section
      top_allocations = main_report.allocated_objects_by_location.first(15)
      allocated_memory_by_location = main_report.allocated_memory_by_location.to_h { |item| [item[:data], item[:count]] }

      <<~HTML
        <div class="section">
            <h2>📍 Top Memory Allocation Locations</h2>
            <p>These are the code locations that allocated the most objects during logging operations:</p>
            <table class="memory-table">
                <tr><th>Location</th><th>Objects Allocated</th><th>Memory</th><th>% of Total</th></tr>
                #{top_allocations.map do |item|
                  location = item[:data]
                  count = item[:count]
                  memory = allocated_memory_by_location.fetch(location, 0)
                  percentage = (count.to_f / main_report.total_allocated * 100).round(2)

                  # Clean up malformed location data and escape HTML
                  cleaned_location = clean_location_for_display(location)
                  escaped_location = html_escape(cleaned_location)

                  "<tr><td class='code-location'>#{escaped_location}</td><td>#{count}</td><td>#{format_bytes(memory)}</td><td>#{percentage}%</td></tr>"
                end.join("\n")}
            </table>
        </div>
      HTML
    end

    def retention_analysis_section
      top_retentions = main_report.retained_objects_by_location.first(15)

      <<~HTML
        <div class="section">
            <h2>🔒 Memory Retention Analysis</h2>
            <p>These locations are retaining objects in memory (potential memory leaks):</p>
            #{if top_retentions.empty?
              '<div class="success">✅ No significant memory retention detected! This is good news.</div>'
              else
              retained_memory_by_location = main_report.retained_memory_by_location.to_h { |item| [item[:data], item[:count]] }
              <<~RETENTION_HTML
                <div class="warning">⚠️ Memory retention detected at the following locations:</div>
                <table class="memory-table">
                    <tr><th>Location</th><th>Objects Retained</th><th>Memory Retained</th><th>% of Total</th></tr>
                    #{top_retentions.map do |item|
                      location = item[:data]
                      count = item[:count]
                      memory = retained_memory_by_location.fetch(location, 0)
                      percentage = (count.to_f / main_report.total_retained * 100).round(2)

                      # Clean up malformed location data and escape HTML
                      cleaned_location = clean_location_for_display(location)
                      escaped_location = html_escape(cleaned_location)

                      "<tr><td class='code-location'>#{escaped_location}</td><td>#{count}</td><td>#{format_bytes(memory)}</td><td>#{percentage}%</td></tr>"
                    end.join("\n")}
                </table>
              RETENTION_HTML
              end}
        </div>
      HTML
    end

    def file_analysis_section
      file_allocations = {}
      main_report.allocated_objects_by_location.each do |item|
        location = item[:data]
        count = item[:count]
        file = extract_file_from_location(location)
        file_allocations[file] = (file_allocations[file] || 0) + count
      end

      top_files = file_allocations.sort_by { |_, count| -count }.first(10)

      <<~HTML
        <div class="section">
            <h2>📁 Memory Usage by File</h2>
            <p>Files contributing most to memory allocation:</p>
            <table class="memory-table">
                <tr><th>File</th><th>Objects Allocated</th><th>% of Total</th></tr>
                #{top_files.map do |file, count|
                  percentage = (count.to_f / main_report.total_allocated * 100).round(2)
                  file_display = file.include?('sentry') ? "<strong>#{file}</strong>" : file
                  "<tr><td class='code-location'>#{file_display}</td><td>#{count}</td><td>#{percentage}%</td></tr>"
                end.join("\n")}
            </table>
        </div>
      HTML
    end

    def format_bytes(bytes)
      return "0 B" if bytes == 0

      units = ["B", "KB", "MB", "GB"]
      size = bytes.to_f
      unit_index = 0

      while size >= 1024 && unit_index < units.length - 1
        size /= 1024
        unit_index += 1
      end

      "#{size.round(2)} #{units[unit_index]}"
    end

    def recommendations_section
      retention_rate = (main_report.total_retained.to_f / main_report.total_allocated * 100).round(2)
      memory_per_iteration = main_report.total_allocated_memsize / iterations

      recommendations = []

      if retention_rate > 10
        recommendations << "🔴 High memory retention rate (#{retention_rate}%) detected. Review the retention analysis section for potential memory leaks."
      elsif retention_rate > 5
        recommendations << "🟡 Moderate memory retention rate (#{retention_rate}%). Monitor for potential memory growth over time."
      else
        recommendations << "🟢 Low memory retention rate (#{retention_rate}%) - this is good!"
      end

      if memory_per_iteration > 1024 * 1024 # 1MB per iteration
        recommendations << "🔴 High memory usage per iteration (#{format_bytes(memory_per_iteration)}). Consider optimizing log message creation."
      elsif memory_per_iteration > 512 * 1024 # 512KB per iteration
        recommendations << "🟡 Moderate memory usage per iteration (#{format_bytes(memory_per_iteration)}). Room for optimization."
      else
        recommendations << "🟢 Reasonable memory usage per iteration (#{format_bytes(memory_per_iteration)})."
      end

      # Convert arrays to hashes for easier lookup
      allocated_by_class = main_report.allocated_objects_by_class.to_h { |item| [item[:data], item[:count]] }

      # Check for string allocations
      string_allocations = allocated_by_class["String"] || 0
      if string_allocations > main_report.total_allocated * 0.3
        recommendations << "🔴 High string allocation rate (#{((string_allocations.to_f / main_report.total_allocated) * 100).round(1)}%). Consider string optimization techniques."
      end

      # Check for hash allocations
      hash_allocations = allocated_by_class["Hash"] || 0
      if hash_allocations > main_report.total_allocated * 0.2
        recommendations << "🟡 Significant hash allocation (#{((hash_allocations.to_f / main_report.total_allocated) * 100).round(1)}%). Review hash usage in logging."
      end

      <<~HTML
        <div class="section">
            <h2>💡 Performance Recommendations</h2>
            <div style="margin: 15px 0;">
                #{recommendations.map { |rec| "<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 3px;'>#{rec}</div>" }.join("\n")}
            </div>

            <h3>General Optimization Tips:</h3>
            <ul>
                <li><strong>String Optimization:</strong> Use frozen strings and avoid unnecessary string concatenation</li>
                <li><strong>Hash Optimization:</strong> Pre-allocate hashes when possible and avoid creating temporary hashes</li>
                <li><strong>Log Sampling:</strong> Consider implementing log sampling for high-volume applications</li>
                <li><strong>Async Logging:</strong> Use background processing for non-critical log events</li>
                <li><strong>Memory Monitoring:</strong> Run this benchmark regularly to track memory usage trends</li>
            </ul>

            <h3>Sentry-Specific Tips:</h3>
            <ul>
                <li><strong>Log Levels:</strong> Use appropriate log levels to reduce unnecessary processing</li>
                <li><strong>Structured Data:</strong> Minimize the size of structured data passed to log events</li>
                <li><strong>Before Send Hooks:</strong> Use before_send_log callbacks to filter out unnecessary events</li>
                <li><strong>Transport Configuration:</strong> Optimize transport settings for your use case</li>
            </ul>
        </div>

        <div class="section">
            <h2>📋 Raw Memory Profile Data</h2>
            <details>
                <summary>Click to view detailed memory profiler output</summary>
                <pre>#{main_report.pretty_print(to_file: nil, detailed_report: false)}</pre>
            </details>
        </div>
      HTML
    end

    def format_bytes(bytes)
      return "0 B" if bytes == 0

      units = ["B", "KB", "MB", "GB"]
      size = bytes.to_f
      unit_index = 0

      while size >= 1024 && unit_index < units.length - 1
        size /= 1024
        unit_index += 1
      end

      "#{size.round(2)} #{units[unit_index]}"
    end

    def format_number(number)
      number.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
    end

    # Extract the file portion from a location string, handling special cases like <internal:module>:line
    def extract_file_from_location(location)
      # Handle internal Ruby locations like "<internal:timev>:265" or "<internal:pack>:25"
      if location.start_with?('<') && location.include?('>')
        # Find the closing > and include it in the file part
        closing_bracket_index = location.index('>')
        if closing_bracket_index
          return location[0..closing_bracket_index]
        end
      end

      # For normal file paths, split on colon and take all parts except the last (line number)
      # This handles cases like "/path/to/file.rb:123"
      parts = location.split(':')
      if parts.length > 1
        # Join all parts except the last one (which should be the line number)
        return parts[0..-2].join(':')
      end

      # If no colon found, return the whole location
      location
    end

    # Clean up malformed location data for display
    def clean_location_for_display(location)
      return "unknown" if location.nil? || location.empty?

      # Handle malformed locations that start with just ":line_number"
      if location.start_with?(':') && location.match?(/^:\d+$/)
        return "<unknown>#{location}"
      end

      # Return the location as-is if it looks valid
      location
    end

    # Simple HTML escaping for location strings
    def html_escape(str)
      str.to_s.gsub('&', '&amp;').gsub('<', '&lt;').gsub('>', '&gt;').gsub('"', '&quot;').gsub("'", '&#39;')
    end
  end
end
