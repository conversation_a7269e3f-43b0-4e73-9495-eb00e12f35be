# frozen_string_literal: true

require_relative "burnit/version"
require_relative "burnit/runner"
require_relative "burnit/git_manager"
require_relative "burnit/performance_monitor"
require_relative "burnit/results_writer"
require_relative "burnit/cli"

module Burnit
  class Error < StandardError; end

  # Main entry point for running commands in git repositories
  def self.run(options = {})
    runner = Runner.new(options)
    runner.run
  end
end
