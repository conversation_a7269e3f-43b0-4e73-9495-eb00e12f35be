# frozen_string_literal: true

module Burnit
  class PerformanceMonitor
    attr_reader :results

    def initialize
      @results = {}
      @memory_samples = []
      @monitoring_thread = nil
    end

    def monitor(&block)
      start_time = Time.now
      start_memory = get_memory_usage
      
      # Start memory monitoring in background thread
      start_memory_monitoring
      
      begin
        # Execute the block
        result = yield
        
        end_time = Time.now
        end_memory = get_memory_usage
        
        # Stop memory monitoring
        stop_memory_monitoring
        
        # Calculate performance metrics
        @results = {
          memory_usage: calculate_memory_metrics(start_memory, end_memory),
          execution_time: calculate_time_metrics(start_time, end_time)
        }
        
        result
      rescue => e
        stop_memory_monitoring
        raise e
      end
    end

    private

    def start_memory_monitoring
      @monitoring = true
      @memory_samples = []
      
      @monitoring_thread = Thread.new do
        while @monitoring
          @memory_samples << get_memory_usage
          sleep 0.1  # Sample every 100ms
        end
      end
    end

    def stop_memory_monitoring
      @monitoring = false
      @monitoring_thread&.join
    end

    def get_memory_usage
      # Get memory usage in KB, convert to MB
      if RUBY_PLATFORM =~ /darwin/
        # macOS
        memory_kb = `ps -o rss= -p #{Process.pid}`.strip.to_f
      elsif RUBY_PLATFORM =~ /linux/
        # Linux
        memory_kb = `ps -o rss= -p #{Process.pid}`.strip.to_f
      else
        # Fallback - try to get from /proc if available
        if File.exist?("/proc/#{Process.pid}/status")
          status = File.read("/proc/#{Process.pid}/status")
          if match = status.match(/VmRSS:\s+(\d+)\s+kB/)
            memory_kb = match[1].to_f
          else
            memory_kb = 0
          end
        else
          memory_kb = 0
        end
      end
      
      memory_kb / 1024.0  # Convert to MB
    end

    def calculate_memory_metrics(start_memory, end_memory)
      if @memory_samples.empty?
        # Fallback if no samples were collected
        peak_memory = [start_memory, end_memory].max
        average_memory = (start_memory + end_memory) / 2.0
      else
        all_samples = [start_memory] + @memory_samples + [end_memory]
        peak_memory = all_samples.max
        average_memory = all_samples.sum / all_samples.length.to_f
      end

      {
        peak_memory_mb: peak_memory.round(2),
        average_memory_mb: average_memory.round(2)
      }
    end

    def calculate_time_metrics(start_time, end_time)
      elapsed_seconds = (end_time - start_time).round(3)
      
      {
        elapsed_seconds: elapsed_seconds,
        start_time: start_time.utc.iso8601,
        end_time: end_time.utc.iso8601
      }
    end
  end
end
