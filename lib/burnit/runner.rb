# frozen_string_literal: true

require 'pathname'
require 'fileutils'
require 'json'
require 'time'

module Burnit
  class Runner
    attr_reader :options

    def initialize(options = {})
      validate_options!(options)
      
      @options = {
        name: "Command Execution",
        description: "Execute command in git repository",
        git_reference: nil,
        repository_path: Dir.pwd,
        verbose: true
      }.merge(options)

      @git_manager = GitManager.new(
        target_sha: @options[:git_reference], 
        repository_path: @options[:repository_path],
        verbose: @options[:verbose]
      )
    end

    def run
      validate_repository!
      
      result = {}
      
      @git_manager.with_commit_sha do
        puts_verbose "=== Burnit Command Execution ==="
        puts_verbose "Command: #{@options[:command]}"
        puts_verbose "Git Reference: #{@options[:git_reference] || 'current'}"
        puts_verbose "Repository: #{@options[:repository_path]}"
        puts_verbose

        # Execute command with performance monitoring
        performance_monitor = PerformanceMonitor.new
        execution_result = performance_monitor.monitor do
          execute_command(@options[:command])
        end

        # Prepare result data
        result = {
          success: execution_result[:success],
          exit_code: execution_result[:exit_code],
          error_message: execution_result[:error_message],
          performance: performance_monitor.results,
          git_info: get_git_info
        }

        # Write results to JSON file
        results_writer = ResultsWriter.new(@options[:repository_path])
        results_writer.write_results(
          task: {
            name: @options[:name],
            description: @options[:description],
            command: @options[:command],
            git_reference: @options[:git_reference] || get_current_git_reference
          },
          performance: result[:performance],
          status: {
            success: result[:success],
            exit_code: result[:exit_code],
            error_message: result[:error_message]
          },
          metadata: {
            burnit_version: Burnit::VERSION,
            timestamp: Time.now.utc.iso8601,
            repository_path: @options[:repository_path]
          }
        )

        puts_verbose "=== Execution Complete ==="
        puts_verbose "Results saved to burnit-results.json"
      end

      result
    end

    private

    def validate_options!(options)
      raise ArgumentError, "command is required" unless options[:command]
    end

    def validate_repository!
      repo_path = @options[:repository_path]
      
      unless Dir.exist?(repo_path)
        raise Error, "Repository path does not exist: #{repo_path}"
      end
      
      unless Dir.exist?(File.join(repo_path, '.git'))
        raise Error, "Not a git repository: #{repo_path}"
      end
    end

    def execute_command(command)
      puts_verbose "Executing: #{command}"
      
      start_time = Time.now
      
      # Change to repository directory and execute command
      Dir.chdir(@options[:repository_path]) do
        # Capture both stdout and stderr, and get exit status
        output = `#{command} 2>&1`
        exit_code = $?.exitstatus
        
        end_time = Time.now
        
        if @options[:verbose]
          puts "Command output:"
          puts output unless output.empty?
          puts "Exit code: #{exit_code}"
        end
        
        {
          success: exit_code == 0,
          exit_code: exit_code,
          error_message: exit_code != 0 ? output.strip : nil,
          output: output,
          start_time: start_time,
          end_time: end_time
        }
      end
    end

    def get_git_info
      Dir.chdir(@options[:repository_path]) do
        {
          sha: `git rev-parse HEAD`.strip,
          short_sha: `git rev-parse --short HEAD`.strip,
          branch: `git rev-parse --abbrev-ref HEAD`.strip,
          commit_message: `git log -1 --pretty=%B`.strip,
          author: `git log -1 --pretty=%an`.strip,
          date: `git log -1 --pretty=%ai`.strip
        }
      end
    end

    def get_current_git_reference
      Dir.chdir(@options[:repository_path]) do
        `git rev-parse --abbrev-ref HEAD`.strip
      end
    end

    def puts_verbose(message)
      puts message if @options[:verbose]
    end
  end
end
