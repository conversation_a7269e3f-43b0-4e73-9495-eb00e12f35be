# frozen_string_literal: true

require "optparse"

module Burnit
  class CLI
    def self.run(args = ARGV)
      new.run(args)
    end

    def run(args)
      options = parse_options(args)

      if options[:help]
        puts @parser
        return
      end

      if options[:version]
        puts "burnit version #{Burnit::VERSION}"
        return
      end

      begin
        runner = Runner.new(options)
        result = runner.run

        if result[:success]
          puts "✓ Command executed successfully"
        else
          puts "✗ Command failed with exit code #{result[:exit_code]}"
          exit result[:exit_code]
        end
      rescue Error => e
        puts "Error: #{e.message}"
        exit 1
      rescue StandardError => e
        puts "Unexpected error: #{e.message}"
        puts e.backtrace if options[:verbose]
        exit 1
      end
    end

    private

    def parse_options(args)
      options = {
        verbose: true,
        repository_path: Dir.pwd
      }

      @parser = OptionParser.new do |opts|
        opts.banner = "Usage: burnit [options]"
        opts.separator ""
        opts.separator "Required options:"

        opts.on("-c", "--command COMMAND", "Command to execute") do |command|
          options[:command] = command
        end

        opts.separator ""
        opts.separator "Optional options:"

        opts.on("-r", "--git-reference REF",
                "Git reference (commit, branch, tag) to checkout before execution") do |ref|
          options[:git_reference] = ref
        end

        opts.on("-n", "--name NAME", "Task name for the results") do |name|
          options[:name] = name
        end

        opts.on("-d", "--description DESC", "Task description for the results") do |desc|
          options[:description] = desc
        end

        opts.on("-p", "--repository-path PATH",
                "Path to git repository (default: current directory)") do |path|
          options[:repository_path] = path
        end

        opts.on("-q", "--quiet", "Run quietly (suppress output)") do
          options[:verbose] = false
        end

        opts.on("-v", "--verbose", "Run with verbose output (default)") do
          options[:verbose] = true
        end

        opts.on("--version", "Show version") do
          options[:version] = true
        end

        opts.on("-h", "--help", "Show this help message") do
          options[:help] = true
        end

        opts.separator ""
        opts.separator "Examples:"
        opts.separator "    burnit --command 'bundle exec rspec'"
        opts.separator "    burnit --command 'rake test' --git-reference abc123"
        opts.separator "    burnit --command 'npm test' --git-reference feature/new-feature"
        opts.separator "    burnit --command 'make test' --git-reference v1.2.3"
        opts.separator ""
        opts.separator "The command will be executed in the specified git repository at the"
        opts.separator "specified git reference. Performance metrics and results will be"
        opts.separator "saved to 'burnit-results.json' in the repository root."
      end

      @parser.parse!(args)

      # Validate required options
      unless options[:command] || options[:help] || options[:version]
        puts "Error: --command is required"
        puts @parser
        exit 1
      end

      options
    end
  end
end
