# frozen_string_literal: true

require 'json'
require 'fileutils'

module Burnit
  class ResultsWriter
    RESULTS_FILENAME = 'burnit-results.json'

    def initialize(repository_path)
      @repository_path = repository_path
      @results_file = File.join(repository_path, RESULTS_FILENAME)
    end

    def write_results(task:, performance:, status:, metadata:)
      results_data = {
        task: task,
        performance: performance,
        status: status,
        metadata: metadata
      }

      validate_schema!(results_data)

      File.write(@results_file, JSON.pretty_generate(results_data))
      
      @results_file
    end

    private

    def validate_schema!(data)
      # Validate required top-level keys
      required_keys = %w[task performance status metadata]
      required_keys.each do |key|
        raise Error, "Missing required key: #{key}" unless data.key?(key.to_sym)
      end

      # Validate task structure
      validate_task_structure!(data[:task])
      
      # Validate performance structure
      validate_performance_structure!(data[:performance])
      
      # Validate status structure
      validate_status_structure!(data[:status])
      
      # Validate metadata structure
      validate_metadata_structure!(data[:metadata])
    end

    def validate_task_structure!(task)
      required_keys = %w[name description command git_reference]
      required_keys.each do |key|
        raise Error, "Missing task key: #{key}" unless task.key?(key.to_sym)
      end

      raise Error, "Task name must be a string" unless task[:name].is_a?(String)
      raise Error, "Task description must be a string" unless task[:description].is_a?(String)
      raise Error, "Task command must be a string" unless task[:command].is_a?(String)
      raise Error, "Task git_reference must be a string" unless task[:git_reference].is_a?(String)
    end

    def validate_performance_structure!(performance)
      required_keys = %w[memory_usage execution_time]
      required_keys.each do |key|
        raise Error, "Missing performance key: #{key}" unless performance.key?(key.to_sym)
      end

      # Validate memory_usage structure
      memory_keys = %w[peak_memory_mb average_memory_mb]
      memory_keys.each do |key|
        unless performance[:memory_usage].key?(key.to_sym)
          raise Error, "Missing memory_usage key: #{key}"
        end
        unless performance[:memory_usage][key.to_sym].is_a?(Numeric)
          raise Error, "Memory usage #{key} must be numeric"
        end
      end

      # Validate execution_time structure
      time_keys = %w[elapsed_seconds start_time end_time]
      time_keys.each do |key|
        unless performance[:execution_time].key?(key.to_sym)
          raise Error, "Missing execution_time key: #{key}"
        end
      end

      unless performance[:execution_time][:elapsed_seconds].is_a?(Numeric)
        raise Error, "Elapsed seconds must be numeric"
      end

      # Validate ISO8601 time format
      begin
        Time.parse(performance[:execution_time][:start_time])
        Time.parse(performance[:execution_time][:end_time])
      rescue ArgumentError => e
        raise Error, "Invalid time format: #{e.message}"
      end
    end

    def validate_status_structure!(status)
      required_keys = %w[success exit_code error_message]
      required_keys.each do |key|
        raise Error, "Missing status key: #{key}" unless status.key?(key.to_sym)
      end

      unless [true, false].include?(status[:success])
        raise Error, "Status success must be boolean"
      end

      unless status[:exit_code].is_a?(Integer)
        raise Error, "Exit code must be integer"
      end

      unless status[:error_message].nil? || status[:error_message].is_a?(String)
        raise Error, "Error message must be string or nil"
      end
    end

    def validate_metadata_structure!(metadata)
      required_keys = %w[burnit_version timestamp repository_path]
      required_keys.each do |key|
        raise Error, "Missing metadata key: #{key}" unless metadata.key?(key.to_sym)
      end

      unless metadata[:burnit_version].is_a?(String)
        raise Error, "Burnit version must be string"
      end

      unless metadata[:repository_path].is_a?(String)
        raise Error, "Repository path must be string"
      end

      # Validate ISO8601 timestamp format
      begin
        Time.parse(metadata[:timestamp])
      rescue ArgumentError => e
        raise Error, "Invalid timestamp format: #{e.message}"
      end
    end
  end
end
