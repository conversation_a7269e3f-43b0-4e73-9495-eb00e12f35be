PATH
  remote: .
  specs:
    sentry-bench (1.0.0)
      benchmark-ipsa
      memory_profiler

GEM
  remote: https://rubygems.org/
  specs:
    ast (2.4.3)
    benchmark-ips (2.5.0)
    benchmark-ipsa (0.2.0)
      benchmark-ips (~> 2.5.0)
      memory_profiler (~> 0.9.6)
    diff-lcs (1.6.2)
    json (2.12.2)
    language_server-protocol (********)
    lint_roller (1.1.0)
    memory_profiler (0.9.14)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    prism (1.4.0)
    racc (1.8.1)
    rainbow (3.1.1)
    rake (13.2.1)
    regexp_parser (2.10.0)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.4)
    rubocop (1.75.7)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    ruby-progressbar (1.13.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)

PLATFORMS
  aarch64-linux
  ruby

DEPENDENCIES
  bundler (~> 2.0)
  rake (~> 13.0)
  rspec (~> 3.0)
  rubocop (~> 1.21)
  sentry-bench!

BUNDLED WITH
   2.6.3
