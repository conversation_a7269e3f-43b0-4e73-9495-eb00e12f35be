# frozen_string_literal: true

require_relative 'test_helper'

describe Burnit do
  describe "when running a command in a git repository" do
    before do
      @repo_dir = create_test_git_repo
      @results_file = File.join(@repo_dir, 'burnit-results.json')
    end
    
    after do
      FileUtils.rm_rf(@repo_dir) if Dir.exist?(@repo_dir)
    end
    
    it "executes the command and stores results in JSON format" do
      # Given a git repository with a test script
      # When I run burnit with a command
      result = Burnit.run(
        command: "ruby test_script.rb",
        git_reference: "main",
        name: "Test Command",
        description: "A test command execution",
        repository_path: @repo_dir
      )
      
      # Then it should execute successfully
      assert result[:success], "Command should execute successfully"
      
      # And it should create a results file
      assert File.exist?(@results_file), "Results file should be created"
      
      # And the results should follow the correct JSON schema
      json_data = JSON.parse(File.read(@results_file))
      validate_burnit_result_schema(json_data)
      
      # And it should contain the expected task information
      assert_equal "Test Command", json_data['task']['name']
      assert_equal "A test command execution", json_data['task']['description']
      assert_equal "ruby test_script.rb", json_data['task']['command']
      assert_equal "main", json_data['task']['git_reference']
      
      # And it should contain performance metrics
      assert json_data['performance']['memory_usage']['peak_memory_mb'] > 0
      assert json_data['performance']['memory_usage']['average_memory_mb'] > 0
      assert json_data['performance']['execution_time']['elapsed_seconds'] > 0
      
      # And it should contain status information
      assert_equal true, json_data['status']['success']
      assert_equal 0, json_data['status']['exit_code']
      assert_nil json_data['status']['error_message']
      
      # And it should contain metadata
      assert_equal Burnit::VERSION, json_data['metadata']['burnit_version']
      assert_equal @repo_dir, json_data['metadata']['repository_path']
      refute_nil json_data['metadata']['timestamp']
    end
    
    it "handles command failures gracefully" do
      # Given a git repository
      # When I run burnit with a failing command
      result = Burnit.run(
        command: "exit 1",
        git_reference: "main",
        name: "Failing Command",
        description: "A command that fails",
        repository_path: @repo_dir
      )
      
      # Then it should handle the failure
      refute result[:success], "Command should fail"
      
      # And it should create a results file
      assert File.exist?(@results_file), "Results file should be created even for failures"
      
      # And the results should indicate failure
      json_data = JSON.parse(File.read(@results_file))
      assert_equal false, json_data['status']['success']
      assert_equal 1, json_data['status']['exit_code']
    end
    
    it "switches to the specified git reference before executing" do
      # Given a git repository with multiple branches
      # When I run burnit with a specific git reference
      result = Burnit.run(
        command: "git rev-parse --abbrev-ref HEAD",
        git_reference: "feature-branch",
        name: "Branch Test",
        description: "Test git reference switching",
        repository_path: @repo_dir
      )
      
      # Then it should execute successfully
      assert result[:success], "Command should execute successfully"
      
      # And it should have switched to the correct branch
      json_data = JSON.parse(File.read(@results_file))
      assert_equal "feature-branch", json_data['task']['git_reference']
    end
    
    it "restores the original git state after execution" do
      # Given a git repository on main branch
      original_branch = nil
      Dir.chdir(@repo_dir) do
        original_branch = `git rev-parse --abbrev-ref HEAD`.strip
      end
      
      # When I run burnit with a different git reference
      Burnit.run(
        command: "echo 'test'",
        git_reference: "feature-branch",
        name: "State Test",
        description: "Test git state restoration",
        repository_path: @repo_dir
      )
      
      # Then it should restore the original branch
      current_branch = nil
      Dir.chdir(@repo_dir) do
        current_branch = `git rev-parse --abbrev-ref HEAD`.strip
      end
      
      assert_equal original_branch, current_branch, "Should restore original git state"
    end
  end
  
  describe "when validating input parameters" do
    it "requires a command to be specified" do
      assert_raises(ArgumentError) do
        Burnit.run(git_reference: "main")
      end
    end
    
    it "uses current directory as default repository path" do
      # This test would need to be run in a git repository
      # For now, we'll just ensure the parameter is handled correctly
      assert_raises(Burnit::Error) do
        Burnit.run(
          command: "echo test",
          repository_path: "/nonexistent/path"
        )
      end
    end
  end
end
