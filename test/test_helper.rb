# frozen_string_literal: true

require "minitest/autorun"
require "minitest/spec"
require "json"
require "tmpdir"
require "fileutils"
require_relative "../lib/burnit"

class Minitest::Spec
  # Helper method to create a temporary git repository for testing
  def create_test_git_repo(name = "test_repo")
    temp_dir = Dir.mktmpdir
    repo_dir = File.join(temp_dir, name)

    # Track temp directory for cleanup
    track_temp_dir(temp_dir)

    # Create the repository directory
    FileUtils.mkdir_p(repo_dir)

    # Initialize git repository
    Dir.chdir(repo_dir) do
      system("git init --quiet")
      system('git config user.email "<EMAIL>"')
      system('git config user.name "Test User"')

      # Create initial commit
      File.write("README.md", "# Test Repository\n\nThis is a test repository.")
      system("git add README.md")
      system('git commit -m "Initial commit" --quiet')

      # Create a test script that we can run
      File.write("test_script.rb", <<~RUBY)
        #!/usr/bin/env ruby
        puts "Test script executed successfully"
        puts "Memory usage: #{`ps -o rss= -p #{Process.pid}`.strip} KB"
        sleep 0.1  # Simulate some work
        exit 0
      RUBY
      system("chmod +x test_script.rb")
      system("git add test_script.rb")
      system('git commit -m "Add test script" --quiet')

      # Create a branch for testing
      system("git checkout -b feature-branch --quiet")
      File.write("feature.txt", "Feature file content")
      system("git add feature.txt")
      system('git commit -m "Add feature file" --quiet')
      system("git checkout main --quiet")

      # Create a tag
      system("git tag v1.0.0 --quiet")
    end

    repo_dir
  end

  # Helper method to validate JSON schema
  def validate_burnit_result_schema(json_data)
    required_keys = %w[task performance status metadata]

    # Check top-level structure
    required_keys.each do |key|
      assert json_data.key?(key), "Missing required key: #{key}"
    end

    # Validate task structure
    task_keys = %w[name description command git_reference]
    task_keys.each do |key|
      assert json_data["task"].key?(key), "Missing task key: #{key}"
    end

    # Validate performance structure
    assert json_data["performance"].key?("memory_usage"), "Missing memory_usage"
    assert json_data["performance"].key?("execution_time"), "Missing execution_time"

    memory_keys = %w[peak_memory_mb average_memory_mb]
    memory_keys.each do |key|
      assert json_data["performance"]["memory_usage"].key?(key), "Missing memory key: #{key}"
    end

    time_keys = %w[elapsed_seconds start_time end_time]
    time_keys.each do |key|
      assert json_data["performance"]["execution_time"].key?(key), "Missing time key: #{key}"
    end

    # Validate status structure
    status_keys = %w[success exit_code error_message]
    status_keys.each do |key|
      assert json_data["status"].key?(key), "Missing status key: #{key}"
    end

    # Validate metadata structure
    metadata_keys = %w[burnit_version timestamp repository_path]
    metadata_keys.each do |key|
      assert json_data["metadata"].key?(key), "Missing metadata key: #{key}"
    end
  end

  # Helper method to load fixture data
  def load_fixture(filename)
    fixture_path = File.join(__dir__, "fixtures", filename)
    File.read(fixture_path)
  end

  # Helper method to load JSON fixture
  def load_json_fixture(filename)
    JSON.parse(load_fixture(filename))
  end

  # Clean up temporary directories after tests
  def teardown
    # Clean up any temporary directories created during tests
    if defined?(@temp_dirs) && @temp_dirs
      @temp_dirs.each do |dir|
        FileUtils.rm_rf(dir) if Dir.exist?(dir)
      end
      @temp_dirs = nil
    end
  end

  private

  def track_temp_dir(dir)
    @temp_dirs ||= []
    @temp_dirs << dir
    dir
  end
end
