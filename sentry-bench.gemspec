# frozen_string_literal: true

require_relative "lib/sentry_bench/version"

Gem::Specification.new do |spec|
  spec.name          = "sentry-bench"
  spec.version       = SentryBench::VERSION
  spec.authors       = ["Sentry Team"]
  spec.email         = ["<EMAIL>"]
  spec.summary       = "Performance benchmarking tool for Sentry Ruby SDK"
  spec.description   = "A comprehensive benchmarking tool for measuring memory usage and performance of the Sentry Ruby SDK across different git commits"
  spec.homepage      = "https://github.com/getsentry/sentry-ruby"
  spec.license       = "MIT"
  spec.required_ruby_version = ">= 2.4"

  spec.metadata["homepage_uri"] = spec.homepage
  spec.metadata["source_code_uri"] = "https://github.com/getsentry/sentry-ruby/tree/master/sentry-bench"
  spec.metadata["changelog_uri"] = "https://github.com/getsentry/sentry-ruby/blob/master/CHANGELOG.md"

  # Specify which files should be added to the gem when it is released.
  spec.files = Dir.chdir(File.expand_path(__dir__)) do
    Dir.glob("{lib,bin}/**/*") + Dir.glob("*.{md,txt,gemspec}")
  end

  spec.bindir        = "bin"
  spec.executables   = ["sentry-bench", "sentry-bench-compare"]
  spec.require_paths = ["lib"]

  # Runtime dependencies
  spec.add_dependency "memory_profiler"
  spec.add_dependency "benchmark-ipsa"

  # Development dependencies
  spec.add_development_dependency "bundler", "~> 2.0"
  spec.add_development_dependency "rake", "~> 13.0"
  spec.add_development_dependency "rspec", "~> 3.0"
end
