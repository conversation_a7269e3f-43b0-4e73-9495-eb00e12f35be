<!DOCTYPE html>
<html>
<head>
    <title>Sentry Benchmark Report (sentry-bench)</title>
    <meta charset="utf-8">
    <style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9f4ff; border-radius: 3px; min-width: 200px; }
    .memory-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    .memory-table th, .memory-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .memory-table th { background-color: #f2f2f2; font-weight: bold; }
    .memory-table tr:nth-child(even) { background-color: #f9f9f9; }
    .chart-container { margin: 20px 0; padding: 15px; background: #fafafa; border-radius: 5px; }
    pre { background: #f8f8f8; padding: 15px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 3px; margin: 10px 0; }
    .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 3px; margin: 10px 0; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 3px; margin: 10px 0; }
    .code-location { font-family: monospace; font-size: 11px; color: #666; }
    .progress-bar { width: 100%; background-color: #e0e0e0; border-radius: 3px; overflow: hidden; }
    .progress-fill { height: 20px; background-color: #4CAF50; text-align: center; line-height: 20px; color: white; font-size: 12px; }
    .gem-badge { background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; }
</style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="header">
    <h1>🔍 Sentry Benchmark Report <span class="gem-badge">sentry-bench v1.0.0</span></h1>
    <p><strong>Generated:</strong> 2025-06-09 12:15:25 UTC</p>
    <p><strong>Iterations:</strong> 100,000</p>
    <p><strong>Ruby Version:</strong> 3.4.2</p>
    <p><strong>Sentry Version:</strong> 5.24.0</p>
    <p><strong>Git SHA:</strong> cafd4cdc (2641-increase-memory-usage-in-logging)</p>
    <p><strong>Commit:</strong> Update CHANGELOG</p>
</div>

    <div class="section">
    <h2>📊 Executive Summary</h2>
    <div class="metric">
        <strong>Total Allocated Objects:</strong><br>
        2,920,177
    </div>
    <div class="metric">
        <strong>Total Retained Objects:</strong><br>
        1,598,060
    </div>
    <div class="metric">
        <strong>Memory Allocated:</strong><br>
        445.48 MB
    </div>
    <div class="metric">
        <strong>Memory Retained:</strong><br>
        285.83 MB
    </div>
    <div class="metric">
        <strong>Retention Rate:</strong><br>
        54.72%
    </div>
    <div class="metric">
        <strong>Avg Memory/Iteration:</strong><br>
        4.56 KB
    </div>
</div>

    <div class="section">
    <h2>🚀 Transport Statistics</h2>
    <table class="memory-table">
        <tr><th>Metric</th><th>Count</th><th>Memory Usage</th></tr>
        <tr><td>Events Captured</td><td>0</td><td>0 B</td></tr>
        <tr><td>Envelopes Created</td><td>1011</td><td>37.52 KB</td></tr>
        <tr><td>Log Events</td><td>1011</td><td>76.64 MB</td></tr>
    </table>
</div>

    <div class="section">
    <h2>🧠 Memory Breakdown by Object Type</h2>
    <table class="memory-table">
        <tr><th>Object Type</th><th>Allocated</th><th>Retained</th></tr>
        <tr><td>String</td><td>259085</td><td>179020</td></tr><tr><td>Hash</td><td>2123012</td><td>1314000</td></tr><tr><td>Array</td><td>308050</td><td>103020</td></tr><tr><td>Time</td><td>127010</td><td>0</td></tr><tr><td>Sentry::LogEvent</td><td>101000</td><td>0</td></tr><tr><td>Sentry::Envelope</td><td>1010</td><td>1010</td></tr><tr><td>Sentry::Event</td><td>0</td><td>0</td></tr><tr><td>Logger::LogDevice</td><td>0</td><td>0</td></tr>
    </table>
</div>

    <div class="section">
    <h2>📍 Top Memory Allocation Locations</h2>
    <p>These are the code locations that allocated the most objects during logging operations:</p>
    <table class="memory-table">
        <tr><th>Location</th><th>Objects Allocated</th><th>Memory</th><th>% of Total</th></tr>
        <tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:144</td><td>906970</td><td>138.39 MB</td><td>31.06%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198</td><td>303000</td><td>46.23 MB</td><td>10.38%</td></tr>
<tr><td class='code-location'>&lt;internal:timev&gt;:265</td><td>127010</td><td>9.69 MB</td><td>4.35%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615</td><td>102010</td><td>3.89 MB</td><td>3.49%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510</td><td>101000</td><td>15.41 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196</td><td>101000</td><td>15.41 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222</td><td>101000</td><td>15.41 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:128</td><td>101000</td><td>52.03 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:72</td><td>101000</td><td>15.41 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:76</td><td>101000</td><td>52.4 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:94</td><td>101000</td><td>3.85 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91</td><td>101000</td><td>15.41 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77</td><td>101000</td><td>3.85 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132</td><td>101000</td><td>15.41 MB</td><td>3.46%</td></tr>
<tr><td class='code-location'>/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170</td><td>49998</td><td>1.91 MB</td><td>1.71%</td></tr>
    </table>
</div>

    <div class="section">
    <h2>🔒 Memory Retention Analysis</h2>
    <p>These locations are retaining objects in memory (potential memory leaks):</p>
    <div class="warning">⚠️ Memory retention detected at the following locations:</div>
<table class="memory-table">
    <tr><th>Location</th><th>Objects Retained</th><th>Memory Retained</th><th>% of Total</th></tr>
    <tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:144</td><td>906970</td><td>138.39 MB</td><td>56.75%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:128</td><td>101000</td><td>52.03 MB</td><td>6.32%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:72</td><td>101000</td><td>15.41 MB</td><td>6.32%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:76</td><td>101000</td><td>52.4 MB</td><td>6.32%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:94</td><td>101000</td><td>3.85 MB</td><td>6.32%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91</td><td>101000</td><td>15.41 MB</td><td>6.32%</td></tr>
<tr><td class='code-location'>/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95</td><td>25000</td><td>976.56 KB</td><td>1.56%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/client.rb:276</td><td>2020</td><td>197.27 KB</td><td>0.13%</td></tr>
<tr><td class='code-location'>/workspace/sentry/sentry-ruby/lib/sentry/client.rb:278</td><td>1010</td><td>78.91 KB</td><td>0.06%</td></tr>
</table>

</div>

    <div class="section">
    <h2>📁 Memory Usage by File</h2>
    <p>Files contributing most to memory allocation:</p>
    <table class="memory-table">
        <tr><th>File</th><th>Objects Allocated</th><th>% of Total</th></tr>
        <tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb</strong></td><td>1310971</td><td>44.89%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/client.rb</strong></td><td>412080</td><td>14.11%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb</strong></td><td>303000</td><td>10.38%</td></tr>
<tr><td class='code-location'><strong>/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb</strong></td><td>249995</td><td>8.56%</td></tr>
<tr><td class='code-location'><internal:timev></td><td>127010</td><td>4.35%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb</strong></td><td>102010</td><td>3.49%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb</strong></td><td>101001</td><td>3.46%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/hub.rb</strong></td><td>101000</td><td>3.46%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb</strong></td><td>101000</td><td>3.46%</td></tr>
<tr><td class='code-location'><strong>/workspace/sentry/sentry-ruby/lib/sentry/scope.rb</strong></td><td>101000</td><td>3.46%</td></tr>
    </table>
</div>

    <div class="section">
    <h2>💡 Performance Recommendations</h2>
    <div style="margin: 15px 0;">
        <div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 3px;'>🔴 High memory retention rate (54.72%) detected. Review the retention analysis section for potential memory leaks.</div>
<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 3px;'>🟢 Reasonable memory usage per iteration (4.56 KB).</div>
<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 3px;'>🟡 Significant hash allocation (72.7%). Review hash usage in logging.</div>
    </div>

    <h3>General Optimization Tips:</h3>
    <ul>
        <li><strong>String Optimization:</strong> Use frozen strings and avoid unnecessary string concatenation</li>
        <li><strong>Hash Optimization:</strong> Pre-allocate hashes when possible and avoid creating temporary hashes</li>
        <li><strong>Log Sampling:</strong> Consider implementing log sampling for high-volume applications</li>
        <li><strong>Async Logging:</strong> Use background processing for non-critical log events</li>
        <li><strong>Memory Monitoring:</strong> Run this benchmark regularly to track memory usage trends</li>
    </ul>

    <h3>Sentry-Specific Tips:</h3>
    <ul>
        <li><strong>Log Levels:</strong> Use appropriate log levels to reduce unnecessary processing</li>
        <li><strong>Structured Data:</strong> Minimize the size of structured data passed to log events</li>
        <li><strong>Before Send Hooks:</strong> Use before_send_log callbacks to filter out unnecessary events</li>
        <li><strong>Transport Configuration:</strong> Optimize transport settings for your use case</li>
    </ul>
</div>

<div class="section">
    <h2>📋 Raw Memory Profile Data</h2>
    <details>
        <summary>Click to view detailed memory profiler output</summary>
        <pre></pre>
    </details>
</div>

</body>
</html>
