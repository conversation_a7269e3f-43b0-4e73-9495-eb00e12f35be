{"metadata": {"git_info": {"sha": "cafd4cdcd3a99fbef6cb849db9d72afbc0841e33", "short_sha": "cafd4cdc", "branch": "2641-increase-memory-usage-in-logging", "commit_message": "Update CHANGELOG", "author": "<PERSON>", "date": "2025-06-09 12:07:37 +0000"}, "timestamp": "2025-06-09T12:15:25Z", "iterations": 100000, "execution_time": 13.353556659, "ruby_version": "3.4.2", "sentry_version": "5.24.0", "gem_version": "1.0.0"}, "memory_profile": {"total_allocated": 2920177, "total_retained": 1598060, "total_allocated_memsize": 467120968, "total_retained_memsize": 299711760, "allocated_objects_by_class": [{"data": "Hash", "count": 2123012}, {"data": "Array", "count": 308050}, {"data": "String", "count": 259085}, {"data": "Time", "count": 127010}, {"data": "Sentry::LogEvent", "count": 101000}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1010}, {"data": "Sentry::Envelope::Item", "count": 1010}], "retained_objects_by_class": [{"data": "Hash", "count": 1314000}, {"data": "String", "count": 179020}, {"data": "Array", "count": 103020}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1010}, {"data": "Sentry::Envelope::Item", "count": 1010}], "allocated_objects_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:144", "count": 906970}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 303000}, {"data": "<internal:timev>:265", "count": 127010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 102010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:128", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:72", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:76", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:94", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 101000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 49998}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 49998}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 47000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:153", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:160", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:167", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:174", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 25000}, {"data": "<internal:pack>:25", "count": 6060}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:276", "count": 3030}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:185", "count": 1999}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 1010}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:278", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:284", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:299", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:303", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 1010}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:184", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 1}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:148", "count": 1}], "retained_objects_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:144", "count": 906970}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:128", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:72", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:76", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:94", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 101000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:276", "count": 2020}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:278", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:299", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:303", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 1010}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:185", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 1000}], "allocated_memory_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:144", "count": 145115200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:76", "count": 54944000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:128", "count": 54556160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 48480000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:72", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 16160000}, {"data": "<internal:timev>:265", "count": 10160800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 4080400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:94", "count": 4040000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:77", "count": 4040000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:153", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:160", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:167", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:174", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 1999920}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 1999920}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 1880000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:299", "count": 848400}, {"data": "<internal:pack>:25", "count": 404000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:276", "count": 363600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:303", "count": 161600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 161600}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:184", "count": 160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:118", "count": 160000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 80800}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:278", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 80800}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:185", "count": 79960}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:284", "count": 40400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 40400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 40000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:148", "count": 352}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:671", "count": 256}], "retained_memory_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:144", "count": 145115200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:76", "count": 54944000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:128", "count": 54556160}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:72", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:94", "count": 4040000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 1000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 1000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:299", "count": 848400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:276", "count": 202000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:303", "count": 161600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 161600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:278", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 40400}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:185", "count": 40000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 40000}]}, "transport_stats": {"events": 0, "envelopes": 1011, "log_events": 1011, "events_memory": 0, "envelopes_memory": 38418, "log_events_memory": 80361183}}