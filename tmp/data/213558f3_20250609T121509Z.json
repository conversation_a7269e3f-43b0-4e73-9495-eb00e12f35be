{"metadata": {"git_info": {"sha": "213558f3aa4d8967365dc4c23b9a8128ad9d81e2", "short_sha": "213558f3", "branch": "master", "commit_message": "[sentry-ruby] fix and improve spec suite setup (#2640)", "author": "<PERSON>", "date": "2025-05-28 11:21:54 +0200"}, "timestamp": "2025-06-09T12:15:09Z", "iterations": 100000, "execution_time": 45.641707952, "ruby_version": "3.4.2", "sentry_version": "5.24.0", "gem_version": "1.0.0"}, "memory_profile": {"total_allocated": 9681713, "total_retained": 1395346, "total_allocated_memsize": 966075424, "total_retained_memsize": 228484608, "allocated_objects_by_class": [{"data": "String", "count": 4600044}, {"data": "Hash", "count": 2930278}, {"data": "Array", "count": 1214654}, {"data": "Time", "count": 429888}, {"data": "MatchData", "count": 302817}, {"data": "Range", "count": 101000}, {"data": "Sentry::LogEvent", "count": 101000}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1010}, {"data": "Sentry::Envelope::Item", "count": 1010}, {"data": "Regexp", "count": 8}, {"data": "Symbol", "count": 4}], "retained_objects_by_class": [{"data": "Hash", "count": 1111329}, {"data": "String", "count": 178969}, {"data": "Array", "count": 103020}, {"data": "Sentry::<PERSON><PERSON><PERSON>", "count": 1010}, {"data": "Sentry::Envelope::Item", "count": 1010}, {"data": "Regexp", "count": 8}], "allocated_objects_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 2321631}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:56", "count": 1211276}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:114", "count": 906421}, {"data": "<internal:pack>:25", "count": 612060}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:100", "count": 603604}, {"data": "<internal:timev>:265", "count": 328949}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 303000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:81", "count": 202000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 102010}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 102010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 102010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 102010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:55", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:60", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:61", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:62", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:63", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:64", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:66", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:50", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:51", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:52", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:53", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:71", "count": 101000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 101000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 100939}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 100939}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 100939}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 100939}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 100939}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:66", "count": 100939}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 49998}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 49998}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 47000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:153", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:160", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:167", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:174", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 25000}], "retained_objects_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:114", "count": 906421}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:100", "count": 100939}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 100939}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:66", "count": 100939}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 25000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 25000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 2020}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 1010}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 1010}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:185", "count": 1000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 1000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 18}], "allocated_memory_by_location": [{"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 166360848}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:114", "count": 145027360}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:56", "count": 92864360}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:100", "count": 74629576}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:198", "count": 64640000}, {"data": "<internal:pack>:25", "count": 40804000}, {"data": "<internal:timev>:265", "count": 26315920}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry-ruby.rb:510", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:196", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:60", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:61", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:62", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:63", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/hub.rb:222", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/propagation_context.rb:91", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:50", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:51", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:52", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:53", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:132", "count": 16160000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 16150240}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/random/formatter.rb:174", "count": 8160800}, {"data": "/workspace/gems/3.4.2/gems/securerandom-0.4.1/lib/securerandom.rb:71", "count": 8160800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 8160800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:55", "count": 8080000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:81", "count": 8080000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:194", "count": 8075120}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:268", "count": 8075120}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/configuration.rb:615", "count": 4080400}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:64", "count": 4040000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/event.rb:66", "count": 4040000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/scope.rb:71", "count": 4040000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:231", "count": 4037560}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:85", "count": 4037560}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:66", "count": 4037560}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:153", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:160", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:167", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:174", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:107", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:74", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:85", "count": 4000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:96", "count": 4000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 1999920}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 1999920}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 1880000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1000000}], "retained_memory_by_location": [{"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:114", "count": 145027360}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:100", "count": 54522976}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:48", "count": 16150240}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/log_event.rb:66", "count": 4037560}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:157", "count": 1000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:170", "count": 1000000}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:176", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:106", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:73", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:84", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:95", "count": 1000000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:304", "count": 848200}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:281", "count": 202000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:308", "count": 161600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:313", "count": 161600}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/client.rb:283", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:14", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/utils/uuid.rb:10", "count": 80800}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/envelope.rb:10", "count": 40400}, {"data": "/workspace/gems/3.4.2/gems/sentry-bench-1.0.0/lib/sentry_bench/profiler.rb:185", "count": 40000}, {"data": "/workspace/sentry/sentry-ruby/lib/sentry/structured_logger.rb:117", "count": 40000}, {"data": "/opt/bitnami/ruby/lib/ruby/3.4.0/time.rb:384", "count": 10272}]}, "transport_stats": {"events": 0, "envelopes": 1011, "log_events": 1011, "events_memory": 0, "envelopes_memory": 38418, "log_events_memory": 60791947}}